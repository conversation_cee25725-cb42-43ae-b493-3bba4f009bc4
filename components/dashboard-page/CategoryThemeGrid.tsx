"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Loader2, Check, Edit, Palette, Building } from "lucide-react";
import { toast } from "sonner";
import {
  getThemesByThemeCategory,
  getThemesByProfession,
  getAllThemeCategories,
  getAllProfessionTypes,
  ThemeConfig
} from "@/themes/theme-registry";
import { updatePortfolioTheme, createPortfolioFromTemplateWithProfession } from "@/lib/portfolio-api";
import { User, ThemeCategory, ProfessionType } from "@/lib/types";

interface Portfolio {
  id: string;
  templateId: string;
  isPublished: boolean;
  slug: string;
  themeCategory?: ThemeCategory;
  professionType?: ProfessionType;
}

interface CategoryThemeGridProps {
  user: User | null;
  portfolio: Portfolio | null;
}

// Main category configuration (Personal vs Organization)
const mainCategories = [
  {
    id: 'personal',
    label: 'Personal',
    icon: Palette,
    description: 'Individual portfolio themes'
  },
  {
    id: 'organization',
    label: 'Organization',
    icon: Building,
    description: 'Company and institution themes'
  }
];

// Removed categoryIcons as we're not using sub-categories anymore

const professionLabels: Record<ProfessionType, string> = {
  general: 'General',
  doctor: 'Doctor',
  nurse: 'Nurse',
  'it-professional': 'IT Professional',
  photographer: 'Photographer',
  designer: 'Designer',
  teacher: 'Teacher',
  'business-analyst': 'Business Analyst',
  traveller: 'Travel Blogger',
};

export default function CategoryThemeGrid({ user, portfolio }: CategoryThemeGridProps) {
  const [loadingThemeId, setLoadingThemeId] = useState<string | null>(null);
  const [selectedMainCategory, setSelectedMainCategory] = useState<'personal' | 'organization'>('personal');
  // Removed selectedCategory as we're using profession-based filtering now
  const [selectedProfession, setSelectedProfession] = useState<ProfessionType>('general');

  const queryClient = useQueryClient();
  const router = useRouter();

  // For existing portfolio - theme switching
  const switchThemeMutation = useMutation({
    mutationFn: async ({ portfolioId, templateId }: { portfolioId: string; templateId: string }) => {
      return updatePortfolioTheme(portfolioId, templateId);
    },
    onMutate: (variables) => {
      setLoadingThemeId(variables.templateId);
    },
    onSuccess: async () => {
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['portfolios', user?.uid] }),
        queryClient.refetchQueries({ queryKey: ['portfolios', user?.uid] })
      ]);
      toast.success("Theme applied successfully!");
      setLoadingThemeId(null);
    },
    onError: (error: Error) => {
      toast.error(`Failed to switch theme: ${error.message}`);
      setLoadingThemeId(null);
    }
  });

  // For new portfolio creation
  const createPortfolioMutation = useMutation({
    mutationFn: async ({ user, templateId, professionType }: {
      user: User;
      templateId: string;
      professionType: ProfessionType;
    }) => {
      return createPortfolioFromTemplateWithProfession(user, templateId, professionType);
    },
    onMutate: (variables) => {
      setLoadingThemeId(variables.templateId);

      // Navigate immediately when creation starts (like ThemeGrid does)
      console.log('🚀 Starting portfolio creation, navigating to editor...');
      router.push('/portfolio?creating=true');
      toast.loading("Creating your portfolio...", { id: 'portfolio-creation' });
    },
    onSuccess: (newPortfolio) => {
      console.log('✅ Portfolio created successfully:', newPortfolio);

      // Update cache after successful creation
      queryClient.setQueryData(['portfolios', user?.uid], [newPortfolio]);

      // Dismiss loading toast and show success
      toast.dismiss('portfolio-creation');
      toast.success("Portfolio created successfully!");

      // Clear loading state
      setLoadingThemeId(null);
    },
    onError: (error: Error) => {
      console.error('❌ Portfolio creation failed:', error);

      // Dismiss loading toast and show error
      toast.dismiss('portfolio-creation');
      toast.error(`Failed to create portfolio: ${error.message}`);
      setLoadingThemeId(null);

      // Navigate back to dashboard on error
      router.push('/dashboard');
    }
  });

  const handleThemeAction = (themeId: string, professionType: ProfessionType = 'general') => {
    if (!user) {
      toast.error("You must be logged in to select a theme.");
      return;
    }

    if (portfolio) {
      // Switch theme for existing portfolio
      switchThemeMutation.mutate({ portfolioId: portfolio.id, templateId: themeId });
    } else {
      // Create new portfolio with selected theme and profession type
      createPortfolioMutation.mutate({ user, templateId: themeId, professionType });
    }
  };

  const renderThemeCard = (theme: ThemeConfig) => {
    const isCurrentTheme = portfolio?.templateId === theme.id;
    const isLoading = loadingThemeId === theme.id;

    return (
      <Card key={theme.id} className="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div className="relative aspect-video overflow-hidden">
          {theme.preview && (
            <Image
              src={theme.preview}
              alt={`${theme.name} theme preview`}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
          )}

          {/* Overlay with action buttons */}
          <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <div className="flex gap-2">
              {portfolio ? (
                <Button
                  onClick={() => handleThemeAction(theme.id)}
                  disabled={isLoading || isCurrentTheme}
                  size="sm"
                  variant={isCurrentTheme ? "secondary" : "default"}
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : isCurrentTheme ? (
                    <>
                      <Check className="h-4 w-4 mr-1" />
                      Current
                    </>
                  ) : (
                    <>
                      <Edit className="h-4 w-4 mr-1" />
                      Switch
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={() => handleThemeAction(theme.id, selectedProfession)}
                  disabled={isLoading}
                  size="sm"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <>
                      <Edit className="h-4 w-4 mr-1" />
                      Create
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>

        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="font-semibold text-lg mb-1">{theme.name}</h3>
              <p className="text-sm text-muted-foreground mb-3">{theme.description}</p>

              <div className="flex flex-wrap gap-1 mb-2">
                {theme.tags?.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {theme.isPremium && (
                  <Badge variant="default" className="text-xs bg-gradient-to-r from-purple-500 to-pink-500">
                    Premium
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const categories = getAllThemeCategories();
  const professionTypes = getAllProfessionTypes();

  // Get themes based on selected filters
  const getFilteredThemes = () => {
    if (selectedMainCategory === 'organization') {
      return getThemesByThemeCategory('organization');
    }

    // For personal themes - show all personal themes based on profession
    if (selectedMainCategory === 'personal') {
      // Get themes by profession (which includes general themes)
      const professionThemes = getThemesByProfession(selectedProfession);

      // Also get themes from specific categories if they support the profession
      const categoryThemes = categories
        .filter(cat => cat !== 'organization' && cat !== 'general')
        .flatMap(cat => getThemesByThemeCategory(cat))
        .filter(theme => theme.professionTypes.includes(selectedProfession));

      // Combine and deduplicate
      const allThemes = [...professionThemes, ...categoryThemes];
      return allThemes.filter((theme, index, self) =>
        index === self.findIndex(t => t.id === theme.id)
      );
    }

    return [];
  };

  const filteredThemes = getFilteredThemes();

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">
          {portfolio ? 'Switch Theme' : 'Choose Your Theme'}
        </h2>
        <p className="text-muted-foreground">
          {portfolio
            ? 'Select a new theme for your portfolio'
            : 'Pick a theme that matches your profession and style'
          }
        </p>
      </div>

      {/* Main Category Tabs (Personal vs Organization) */}
      <Tabs value={selectedMainCategory} onValueChange={(value) => setSelectedMainCategory(value as 'personal' | 'organization')} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6 h-auto p-1 bg-muted/50">
          {mainCategories.map((category) => {
            const Icon = category.icon;
            return (
              <TabsTrigger
                key={category.id}
                value={category.id}
                className="flex items-center justify-start gap-3 p-4 h-auto text-left data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm border-0 rounded-md"
              >
                <Icon className="h-5 w-5 flex-shrink-0" />
                <div className="text-left flex-1 min-w-0">
                  <div className="font-medium text-sm truncate">{category.label}</div>
                  <div className="text-xs text-muted-foreground hidden sm:block mt-0.5 truncate">{category.description}</div>
                </div>
              </TabsTrigger>
            );
          })}
        </TabsList>

        {/* Personal Tab Content */}
        <TabsContent value="personal" className="mt-6 space-y-6 focus-visible:outline-none">
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">Select your profession</h3>
              <p className="text-sm text-muted-foreground mb-4">Choose your profession to see the most relevant themes</p>
              <div className="flex flex-wrap gap-2">
                {professionTypes.map((profession) => (
                  <Button
                    key={profession}
                    variant={selectedProfession === profession ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedProfession(profession)}
                    className="transition-all duration-200"
                  >
                    {professionLabels[profession]}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Organization Tab Content */}
        <TabsContent value="organization" className="mt-6 space-y-6 focus-visible:outline-none">
          <div className="text-center py-8">
            <Building className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold mb-2">Organization Themes</h3>
            <p className="text-muted-foreground mb-4">Professional themes for companies and institutions</p>
            <p className="text-sm text-muted-foreground">Coming soon! We&apos;re working on amazing themes for organizations.</p>
          </div>
        </TabsContent>

        {/* Theme Grid - Show for both Personal and Organization */}
        <div className="mt-6">
          {filteredThemes.length > 0 ? (
            <div>
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-2">Available Themes</h3>
                <p className="text-sm text-muted-foreground">
                  {selectedMainCategory === 'personal'
                    ? `Themes optimized for ${professionLabels[selectedProfession].toLowerCase()}s`
                    : 'Professional themes for organizations'
                  }
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredThemes.map(renderThemeCard)}
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <Palette className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No themes available</h3>
              <p className="text-muted-foreground">
                {selectedMainCategory === 'organization'
                  ? 'Organization themes are coming soon!'
                  : `Themes for ${professionLabels[selectedProfession].toLowerCase()}s are coming soon!`
                }
              </p>
            </div>
          )}
        </div>
      </Tabs>
    </div>
  );
}
