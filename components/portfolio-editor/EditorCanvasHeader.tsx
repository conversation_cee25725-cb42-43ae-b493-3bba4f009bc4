"use client";
import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Loader2, Rocket, LayoutDashboard, LogOut, Settings, AlertCircle } from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthenticationContext";
import { useLogout } from "@/hooks/use-logout";
import { useState } from "react";
import { ValidationResult } from "@/lib/portfolio-validation";

interface EditorCanvasHeaderProps {
  isPublishing: boolean;
  isDirty: boolean;
  validationResult?: ValidationResult;
  onTogglePublish: () => void;
  onValidate: () => void;
}

export default function EditorCanvasHeader(props: EditorCanvasHeaderProps) {
  const { user } = useAuth();
  const logout = useLogout();
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    await logout();
    setIsSigningOut(false);
  };

  const hasValidationErrors = props.validationResult && !props.validationResult.isValid;
  // const canPublish = !props.isPublishing && !hasValidationErrors;

  const handlePublishClick = () => {
    if (hasValidationErrors) {
      props.onValidate(); // Show validation errors
      return;
    }
    props.onTogglePublish();
  };

  return (
    <div className="bg-white/95 backdrop-blur-sm border-b border-gray-200 px-4 py-3 flex items-center justify-between">
      <div className="flex items-center gap-4">
        <h2 className="text-sm font-semibold text-gray-900">Portfolio Editor</h2>
        <div className="text-xs text-gray-500 hidden sm:block">
          Make changes to see them reflected in real-time
        </div>
      </div>

      <div className="flex items-center gap-3">
        {/* Back to Dashboard Button */}
        <Button
          asChild
          variant="outline"
          size="sm"
          className="hidden sm:flex"
        >
          <Link href="/dashboard">
            <LayoutDashboard className="mr-2 h-4 w-4" />
            Dashboard
          </Link>
        </Button>

        {/* Publish Button */}
        <Button
          onClick={handlePublishClick}
          disabled={props.isPublishing}
          className={`shadow-sm ${
            hasValidationErrors
              ? "bg-red-500 hover:bg-red-600 text-white"
              : "bg-brandAccent hover:bg-brandAccent/90 text-white"
          }`}
          size="sm"
        >
          {props.isPublishing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Publishing...
            </>
          ) : hasValidationErrors ? (
            <>
              <AlertCircle className="mr-2 h-4 w-4" />
              Fix Issues
            </>
          ) : (
            <>
              <Rocket className="mr-2 h-4 w-4" />
              Publish
            </>
          )}
        </Button>

        {/* User Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="p-0 h-8 w-8 rounded-full hover:bg-gray-100">
              <Avatar className="h-8 w-8 border-2 border-gray-200 hover:border-brandAccent transition-colors">
                <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName || 'User'} />
                <AvatarFallback className="bg-brandAccent text-white font-semibold text-xs">
                  {user?.displayName?.charAt(0)?.toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56 max-w-[calc(100vw-2rem)] z-[9999] bg-white border-gray-200 shadow-xl">
            <div className="flex flex-col px-3 py-3 bg-gray-50 border-b border-gray-200 min-w-0">
              <span className="font-semibold text-gray-800 truncate text-sm">{user?.displayName || 'User'}</span>
              <span className="text-xs text-gray-500 truncate">{user?.email || 'No email'}</span>
            </div>

            {/* Mobile Dashboard Link */}
            <DropdownMenuItem asChild className="sm:hidden hover:bg-blue-50">
              <Link href="/dashboard">
                <LayoutDashboard className="mr-2 h-4 w-4" />
                Dashboard
              </Link>
            </DropdownMenuItem>

            <DropdownMenuItem asChild className="hover:bg-blue-50">
              <Link href="/settings">
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </Link>
            </DropdownMenuItem>

            <DropdownMenuSeparator className="bg-gray-200" />
            <DropdownMenuItem onClick={handleSignOut} disabled={isSigningOut} className="hover:bg-red-50 text-red-600">
              {isSigningOut ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <LogOut className="mr-2 h-4 w-4" />}
              {isSigningOut ? 'Signing out...' : 'Sign out'}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
